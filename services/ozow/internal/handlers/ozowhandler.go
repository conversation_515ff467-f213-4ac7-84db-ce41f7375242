package handlers

import (
	aps_http "aps/lib/generic/http"
	aps_vault "aps/lib/generic/vault"
	"aps/lib/tools"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/auth"
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/database"
	"aps/services/ozow/internal/logging"
	"aps/services/ozow/internal/models"
	"aps/services/ozow/internal/session"
	"bytes"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

var paymentLogger *logging.RedisLogger
var paymentSessionManager *session.SessionManager

func init() {
	paymentLogger = logging.NewRedisLogger()
	paymentSessionManager = session.NewSessionManager()
}

func makePayment(req_body models.PaymentReq, vcred models.MyVaultCred) ([]byte, error) {
	URL := os.Getenv("API") + "/PostPaymentRequest"
	log.Println(URL)
	hash := GenerateHash(req_body, vcred.Data.Privatekey)
	if len(req_body.HashCheck) == 0 {
		req_body.HashCheck = hash
	}
	log.Println(req_body.HashCheck)
	myjson, err := json.Marshal(req_body)
	if err != nil {
		return nil, err
	}
	log.Println("JSON: ", string(myjson))
	body := bytes.NewBuffer(myjson)

	req, err := http.NewRequest(http.MethodPost, URL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set(aps_http.HeaderAccept, aps_http.MIMEApplicationJSON)
	req.Header.Set(aps_http.HeaderContentType, aps_http.MIMEApplicationJSON)
	req.Header.Set("ApiKey", vcred.Data.Apikey)
	log.Println("Key: ", vcred.Data.Apikey)

	res, err := aps_http.RegRequest(req)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func getClientDetails(idNumber string) (models.ClientDetails, error) {
	var clientDetails models.ClientDetails
	db, err := database.GetDBConnection()
	if err != nil {
		return clientDetails, err
	}
	defer db.Close()
	query, err := tools.FetchQuery(assets.SQL, "sql/query.sql")
	if err != nil {
		return clientDetails, err
	}
	rows, err := db.Query(query, idNumber)
	if err != nil {
		return clientDetails, err
	}
	defer rows.Close()
	for rows.Next() {
		err = rows.Scan(
			&clientDetails.ClientKey, &clientDetails.IdentificationDetail, &clientDetails.Employer,
			&clientDetails.Department, &clientDetails.EmployeeCode, &clientDetails.EngagementDate,
			&clientDetails.Payday, &clientDetails.PaydayFrequency, &clientDetails.SalaryAmount,
			&clientDetails.EmploymentType, &clientDetails.FirstName, &clientDetails.Surname,
			&clientDetails.AdditionalNames, &clientDetails.KnownAs, &clientDetails.MaritalStatus,
			&clientDetails.ResidentialBuilding, &clientDetails.ResidentialAddressLine1,
			&clientDetails.ResidentialAddressLine2, &clientDetails.ResidentialAddressLine3,
			&clientDetails.ResidentialAddressLine4, &clientDetails.ResidentialProvince,
			&clientDetails.ResidentialPostalCode, &clientDetails.WorkBuilding,
			&clientDetails.WorkAddressLine1, &clientDetails.WorkAddressLine2,
			&clientDetails.WorkAddressLine3, &clientDetails.WorkAddressLine4,
			&clientDetails.WorkProvince, &clientDetails.WorkPostalCode,
			&clientDetails.MobileNumber, &clientDetails.EmailAddress, &clientDetails.AccountKey,
			&clientDetails.AccountNumber, &clientDetails.ContractKey, &clientDetails.ContractHash,
			&clientDetails.ContractStatus, &clientDetails.TrueBalance, &clientDetails.PtpBalance,
			&clientDetails.SettlementValue, &clientDetails.ActivationDate,
			&clientDetails.FullContractValue, &clientDetails.InstallmentValue,
		)
		if err != nil {
			return clientDetails, err
		}
	}

	if len(clientDetails.ContractKey.String) == 0 {
		return clientDetails, errors.New("No customer details")
	}
	return clientDetails, nil
}
func setFields(req_body *models.PaymentReq, clientDetails *models.ClientDetails, amount float64) {
	req_body.CustomerIdentifier = clientDetails.IdentificationDetail.String
	req_body.SiteCode = os.Getenv("SITE_CODE")
	req_body.CountryCode = "ZA"
	req_body.CurrencyCode = "ZAR"
	req_body.BankReference = "BLC"
	req_body.Optional1 = clientDetails.ContractKey.String
	req_body.Amount = amount
	req_body.IsTest = false
	req_body.TransactionReference = fmt.Sprintf("%s_%d", clientDetails.ContractKey.String, time.Now().Unix())
}

func GenerateHash(req models.PaymentReq, secret string) string {
	fields := []string{
		req.SiteCode,
		req.CountryCode,
		req.CurrencyCode,
		fmt.Sprintf("%.2f", req.Amount),
		req.TransactionReference,
		req.BankReference,
		req.Optional1,
		req.Optional2,
		req.Optional3,
		req.Optional4,
		req.Optional5,
		req.Customer,
		req.CancelURL,
		req.ErrorURL,
		req.SuccessURL,
		req.NotifyURL,
		fmt.Sprintf("%t", req.IsTest),
		req.SelectedBankID,
		req.BankAccountNumber,
		req.BranchCode,
		req.BankAccountName,
		req.PayeeDisplayName,
		req.ExpiryDateUtc,
		req.CustomerIdentifier,
		req.CustomerCellphoneNumber,
		req.HashCheck,
		secret,
	}

	concatenated := ""
	for _, field := range fields {
		if field != "" {
			concatenated += field
			log.Println("F: ", field)
		}

	}
	log.Println("Pre hash: ", concatenated)
	calculatedHashResult := generateRequestHashCheck(concatenated)
	return calculatedHashResult
}

func generateRequestHashCheck(inputString string) string {
	stringToHash := strings.ToLower(inputString)
	fmt.Printf("Before Hashcheck: %s\n", stringToHash)
	return getSha512Hash(stringToHash)
}

func getSha512Hash(stringToHash string) string {
	hasher := sha512.New()
	hasher.Write([]byte(stringToHash))
	hash := hasher.Sum(nil)
	return hex.EncodeToString(hash)
}

func WebPaymentRequest(w http.ResponseWriter, r *http.Request) {
	var req_body models.PaymentReq
	var clientDetails models.ClientDetails
	PATH := os.Getenv("VAULT_PATH")
	VURL := os.Getenv("VAULT_URL")
	TOKEN := os.Getenv("VAULT_TOKEN")
	credbdy, err := aps_vault.GetSecret(PATH, VURL, TOKEN)
	if err != nil {
		aps_http.HandleError(w, http.StatusInternalServerError, nil)
	}
	var vcred models.MyVaultCred
	err = json.Unmarshal(credbdy, &vcred)
	if err != nil {
		log.Println(err)
		err := components.Error("Internal Server Error").Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}
	err = r.ParseForm()
	if err != nil {
		err := components.Error("Error Invalid Data").Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}
	idNumber := r.FormValue("idNumber")
	amountStr := r.FormValue("amount")
	log.Printf("IdNumber: %s, Amount:%s", idNumber, amountStr)
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		log.Printf("Error parsing amount: %v", err)
		err := components.Error("Error Invalid Amount").Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}
	clientDetails, err = getClientDetails(idNumber)
	if err != nil {
		log.Println(err)
		err := components.Error("Error could not get client details ").Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}

	setFields(&req_body, &clientDetails, amount)
	log.Println(req_body)

	// Get user ID from session for logging
	userID, _ := paymentSessionManager.GetUserID(r)
	if userID == "" {
		userID = idNumber // fallback to ID number if no session
	}

	res, err := makePayment(req_body, vcred)
	if err != nil {
		log.Println(err)
		paymentLogger.LogPayment(userID, clientDetails.ContractKey.String, amount, req_body.TransactionReference, "", "error", r, err.Error())
		err := components.Error("Error sending payment").Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}
	log.Println("OZOW: ", string(res))
	var myres models.PaymentRes
	err = json.Unmarshal(res, &myres)
	if err != nil {
		log.Println(err)
		err := components.Error("Error getting payment page").Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}
	log.Println(myres)
	if !myres.ErrorMessage.Valid {
		log.Println(myres.URL)
		paymentLogger.LogPayment(userID, clientDetails.ContractKey.String, amount, req_body.TransactionReference, myres.PaymentRequestID, "initiated", r, "")
		components.Redirect(myres.URL).Render(r.Context(), w)
	} else {
		log.Println(err)
		paymentLogger.LogPayment(userID, clientDetails.ContractKey.String, amount, req_body.TransactionReference, myres.PaymentRequestID, "failed", r, myres.ErrorMessage.String)
		err := components.Error(myres.ErrorMessage.String).Render(r.Context(), w)
		if err != nil {
			log.Println(err)
		}
		return
	}

}

func CreatePaymentRequest(w http.ResponseWriter, r *http.Request) {
	var req_body models.PaymentReq
	var clientDetails models.ClientDetails
	PATH := os.Getenv("VAULT_PATH")
	VURL := os.Getenv("VAULT_URL")
	TOKEN := os.Getenv("VAULT_TOKEN")
	credbdy, err := aps_vault.GetSecret(PATH, VURL, TOKEN)
	if err != nil {
		aps_http.HandleError(w, http.StatusInternalServerError, nil)
	}
	var vcred models.MyVaultCred
	err = json.Unmarshal(credbdy, &vcred)
	if err != nil {
		aps_http.HandleError(w, http.StatusInternalServerError, nil)
	}

	if err := aps_http.DeserializeJSON(r, &req_body); err != nil {
		aps_http.HandleError(w, http.StatusBadRequest, nil)
	}

	if req_body.CustomerIdentifier != "" {
		clientDetails, err = getClientDetails(req_body.CustomerIdentifier)
		if err != nil {
			aps_http.HandleError(w, http.StatusInternalServerError, nil)
		}
		if err := aps_http.DeserializeJSON(r, &req_body); err != nil {
			aps_http.HandleError(w, http.StatusBadRequest, nil)
		}

		req_body.CustomerIdentifier = clientDetails.IdentificationDetail.String
		req_body.SiteCode = os.Getenv("SITE_CODE")
		req_body.SiteCode = "BLU-RUB-001"
		req_body.CountryCode = "ZA"
		req_body.CurrencyCode = "ZAR"
		req_body.BankReference = "BLC"
		req_body.IsTest = false
		req_body.Optional1 = clientDetails.ContractKey.String
		req_body.TransactionReference = fmt.Sprintf("%s_%d", clientDetails.ContractKey.String, time.Now().Unix())

		setFields(&req_body, &clientDetails, req_body.Amount)

	}

	// Get user ID for logging
	userID := req_body.CustomerIdentifier
	if userID == "" {
		userID = "unknown"
	}

	res, err := makePayment(req_body, vcred)
	if err != nil {
		paymentLogger.LogPayment(userID, clientDetails.ContractKey.String, req_body.Amount, req_body.TransactionReference, "", "error", r, err.Error())
		aps_http.HandleError(w, http.StatusInternalServerError, nil)
	}
	w.Header().Add(aps_http.HeaderContentType, aps_http.MIMEApplicationJSON)
	log.Println(string(res))

	// Parse response to get payment request ID for logging
	var paymentRes models.PaymentRes
	if err := json.Unmarshal(res, &paymentRes); err == nil {
		if !paymentRes.ErrorMessage.Valid {
			paymentLogger.LogPayment(userID, clientDetails.ContractKey.String, req_body.Amount, req_body.TransactionReference, paymentRes.PaymentRequestID, "initiated", r, "")
		} else {
			paymentLogger.LogPayment(userID, clientDetails.ContractKey.String, req_body.Amount, req_body.TransactionReference, paymentRes.PaymentRequestID, "failed", r, paymentRes.ErrorMessage.String)
		}
	}

	myres := string(res)
	n, err := io.WriteString(w, string(myres))
	if err != nil {
		log.Println("Code: ", n)
		log.Println("Error: ", err.Error())
	}
}

func Notify(w http.ResponseWriter, r *http.Request) {
	log.Println("Notified transactions,")
	err := r.ParseForm()
	if err != nil {
		log.Println(err)
	}
	var req_body models.Notify
	log.Println("Received form data:")
	log.Println(r.Form)
	req_body.SiteCode = r.Form.Get("SiteCode")
	req_body.TransactionID = r.Form.Get("TransactionId")
	req_body.TransactionReference = r.Form.Get("TransactionReference")
	amountStr := r.Form.Get("Amount")
	if amount, err := strconv.ParseFloat(amountStr, 64); err == nil {
		req_body.Amount = amount
	} else {
		log.Println(err)
	}
	req_body.Status = r.Form.Get("Status")
	req_body.Optional1 = r.Form.Get("Optional1")
	req_body.Optional2 = r.Form.Get("Optional2")
	req_body.Optional3 = r.Form.Get("Optional3")
	req_body.Optional4 = r.Form.Get("Optional4")
	req_body.Optional5 = r.Form.Get("Optional5")
	req_body.CurrencyCode = r.Form.Get("CurrencyCode")
	req_body.Hash = r.Form.Get("Hash")
	req_body.SubStatus = r.Form.Get("SubStatus")
	req_body.MaskedAccountNumber = r.Form.Get("MaskedAccountNumber")
	req_body.BankName = r.Form.Get("BankName")
	req_body.SmartIndicators = r.Form.Get("SmartIndicators")

	// Log payment status update
	userID := "unknown" // We don't have user ID in notification, use contract key as identifier
	if req_body.Optional1 != "" {
		userID = req_body.Optional1 // Use contract key as user identifier
	}

	status := strings.ToLower(req_body.Status)
	errorMsg := ""
	if req_body.Status != "Complete" {
		log.Println(req_body)
		log.Printf("Transaction %s status is %s", req_body.TransactionReference, req_body.Status)
		errorMsg = fmt.Sprintf("Status: %s, SubStatus: %s", req_body.Status, req_body.SubStatus)
	}

	paymentLogger.LogPayment(userID, req_body.Optional1, req_body.Amount, req_body.TransactionReference, req_body.TransactionID, status, r, errorMsg)
	baseURL := os.Getenv("RUBIX")
	token := os.Getenv("RUBIX_TOKEN")

	u, err := url.Parse(baseURL)
	if err != nil {
		log.Println(err)
	}
	now := time.Now().UTC()
	loc, err := time.LoadLocation("Africa/Johannesburg")
	if err != nil {
		fmt.Println("Error loading timezone:", err)
		return
	}

	localTime := now.In(loc)
	query := u.Query()
	query.Set("token", token)
	query.Set("transaction_date", localTime.Format("2006-01-02 15:04:05"))
	query.Set("reference", "OZOW")
	query.Set("amount", fmt.Sprintf("%.2f", req_body.Amount))
	query.Set("promissory_id", req_body.TransactionReference)
	query.Set("contract_key", req_body.Optional1)
	u.RawQuery = query.Encode()
	log.Println("Request URL with query params:", u.String())

	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		log.Println(err)
	}
	myres, err := aps_http.RegRequest(req)
	if err != nil {
		log.Println(err)
	}
	log.Println(string(myres))
	res := "Success"
	w.WriteHeader(http.StatusOK)
	n, err := io.WriteString(w, res)
	if err != nil {
		log.Println("Code: ", n)
		log.Println("Error: ", err.Error())
	}

}

func SubmitRefund(w http.ResponseWriter, r *http.Request) {
	tk, err := auth.GetToken()
	if err != nil {
		log.Println("Error: ", err.Error())
	}
	n, err := io.WriteString(w, string(tk))
	if err != nil {
		log.Println("Code: ", n)
		log.Println("Error: ", err.Error())
	}

}
