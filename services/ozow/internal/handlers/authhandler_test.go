package handlers

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestAuthenticateUser(t *testing.T) {
	tests := []struct {
		name       string
		idNumber   string
		password   string
		expectUser bool
		expectErr  bool
	}{
		{
			name:       "Empty ID number",
			idNumber:   "",
			password:   "somepassword",
			expectUser: false,
			expectErr:  false,
		},
		{
			name:       "Empty password",
			idNumber:   "1234567890123",
			password:   "",
			expectUser: false,
			expectErr:  false,
		},
		{
			name:       "Both empty",
			idNumber:   "",
			password:   "",
			expectUser: false,
			expectErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			user, err := AuthenticateUser(tt.idNumber, tt.password)

			if tt.expectErr && err == nil {
				t.<PERSON>rrorf("Expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.<PERSON>rf("Unexpected error: %v", err)
			}
			if tt.expectUser && user == nil {
				t.Errorf("Expected user but got nil")
			}
			if !tt.expectUser && user != nil {
				t.Errorf("Expected nil user but got: %+v", user)
			}
		})
	}
}

func TestLoginPostHandler(t *testing.T) {
	tests := []struct {
		name           string
		idNumber       string
		password       string
		expectedStatus int
	}{
		{
			name:           "Empty credentials",
			idNumber:       "",
			password:       "",
			expectedStatus: http.StatusOK, // Should render login form with error
		},
		{
			name:           "Empty ID number",
			idNumber:       "",
			password:       "somepassword",
			expectedStatus: http.StatusOK, // Should render login form with error
		},
		{
			name:           "Empty password",
			idNumber:       "1234567890123",
			password:       "",
			expectedStatus: http.StatusOK, // Should render login form with error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create form data
			formData := strings.NewReader("idNumber=" + tt.idNumber + "&password=" + tt.password)

			req, err := http.NewRequest("POST", "/login", formData)
			if err != nil {
				t.Fatal(err)
			}
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(LoginPostHandler)

			handler.ServeHTTP(rr, req)

			if status := rr.Code; status != tt.expectedStatus {
				t.Errorf("Handler returned wrong status code: got %v want %v", status, tt.expectedStatus)
			}
		})
	}
}

func TestRedisLogging(t *testing.T) {
	// Test that Redis logging doesn't crash the application
	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	// This should not panic even if Redis is not available
	if redisLogger != nil {
		redisLogger.LogLoginAttempt("test123", false, req, "test error")
		redisLogger.LogPayment("test123", "contract123", 100.50, "trans123", "pay123", "test", req, "")
	}
}
