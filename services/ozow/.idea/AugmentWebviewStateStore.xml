<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;8effc4bc-e409-4983-ab0b-f8caa1ac8294&quot;,&quot;conversations&quot;:{&quot;8effc4bc-e409-4983-ab0b-f8caa1ac8294&quot;:{&quot;id&quot;:&quot;8effc4bc-e409-4983-ab0b-f8caa1ac8294&quot;,&quot;createdAtIso&quot;:&quot;2025-08-15T06:57:45.182Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-15T07:05:35.842Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;855da241-ac40-496a-b37f-b0f6ad84c855&quot;,&quot;request_message&quot;:&quot;hey&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;hey&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;*You are out of user messages <NAME_EMAIL>. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;hey&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;//wsl.localhost/Ubuntu/home/<USER>/oni/aps-mono/services/ozow&quot;,&quot;folderRoot&quot;:&quot;//wsl.localhost/Ubuntu/home/<USER>/oni/aps-mono/services/ozow&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-15T07:05:35.843Z&quot;,&quot;response_text&quot;:&quot;*You are out of user messages <NAME_EMAIL>. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-dc3249fc-0f8e-49bd-b2b8-d70a7cb647fb&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;1f9febe2-baa2-4904-9a6b-239edd5283c4&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>